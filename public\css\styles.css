/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Progress Bar */
.progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1000;
}

.progress-bar {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 3rem;
    padding-top: 2rem;
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Pages */
.page {
    display: none;
    width: 100%;
    max-width: 500px;
    animation: fadeIn 0.3s ease;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.welcome-card,
.options-card,
.form-card,
.dashboard-card {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.welcome-card h2,
.options-card h2,
.form-card h2,
.dashboard-card h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.welcome-card p,
.options-card p,
.form-card p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1rem;
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #ee82f0 0%, #f3455a 100%);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.btn-subtitle {
    font-size: 0.85rem;
    opacity: 0.8;
    font-weight: 400;
    margin-top: 0.25rem;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 1rem;
    padding: 0.5rem;
    transition: color 0.3s ease;
}

.btn-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Forms */
.form {
    text-align: left;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.85rem;
}

.radio-group {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 400;
}

.radio-label input[type="radio"] {
    width: auto;
    margin-right: 0.5rem;
}

.form-links {
    text-align: center;
    margin-top: 1.5rem;
}

.form-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.form-links a:hover {
    text-decoration: underline;
}

/* Dashboard Styles */
.dashboard-content {
    margin-top: 2rem;
}

.status-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

.status-card h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.progress-indicator {
    margin: 1rem 0;
}

.progress-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(#667eea 0deg, #e1e8ed 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.progress-circle span {
    position: relative;
    z-index: 1;
    font-weight: 600;
    color: #2c3e50;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Admin Dashboard */
.admin-content {
    margin-top: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.stat-card h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.admin-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    flex-direction: column;
}

.loading-overlay.active {
    display: flex;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: white;
    font-size: 1rem;
}

/* Error Messages */
.error-message {
    background: #fee;
    color: #c33;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #fcc;
    font-size: 0.9rem;
}

.success-message {
    background: #efe;
    color: #363;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #cfc;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem 0.5rem;
    }

    .header {
        margin-bottom: 2rem;
        padding-top: 1rem;
    }

    .logo {
        font-size: 2rem;
    }

    .welcome-card,
    .options-card,
    .form-card,
    .dashboard-card {
        padding: 2rem 1.5rem;
        margin: 0 0.5rem;
    }

    .button-group {
        gap: 0.75rem;
    }

    .btn {
        padding: 1rem 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .radio-group {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .welcome-card,
    .options-card,
    .form-card,
    .dashboard-card,
    .assessment-card,
    .question-card,
    .transition-card,
    .results-card {
        padding: 1.5rem 1rem;
    }

    .logo {
        font-size: 1.75rem;
    }

    .btn-icon {
        font-size: 1.25rem;
    }

    .ai-assistant {
        flex-direction: column;
        text-align: center;
    }

    .ai-avatar {
        font-size: 2.5rem;
    }

    .answer-buttons {
        gap: 0.75rem;
    }

    .btn-answer {
        padding: 1rem 1.25rem;
        font-size: 1rem;
        min-height: 50px;
    }

    .question-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .recommendation-card {
        padding: 1.5rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.btn:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        border: 2px solid #fff;
    }

    .btn-outline {
        border-width: 3px;
    }
}

/* Assessment Styles */
.assessment-card,
.question-card,
.transition-card,
.results-card {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

/* AI Assistant */
.ai-assistant {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    text-align: left;
}

.ai-avatar {
    font-size: 3rem;
    flex-shrink: 0;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.ai-message h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.ai-message p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

/* Assessment Info */
.assessment-info {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: #2c3e50;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-icon {
    font-size: 1.25rem;
}

/* Assessment Actions */
.assessment-actions,
.transition-actions,
.results-actions {
    margin-top: 2rem;
}

.btn-large {
    padding: 1.5rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Question Styles */
.question-header {
    margin-bottom: 2rem;
}

.question-progress {
    margin-bottom: 1rem;
}

.question-progress span {
    display: block;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.progress-bar-question {
    width: 100%;
    height: 8px;
    background: #e1e8ed;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill-question {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.section-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
}

/* Question Content */
.question-content {
    margin: 2rem 0;
}

.question-content h2 {
    font-size: 1.5rem;
    color: #2c3e50;
    line-height: 1.4;
    font-weight: 500;
}

/* Answer Buttons */
.answer-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 2rem 0;
}

.btn-answer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1.25rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    border-radius: 12px;
    transition: all 0.3s ease;
    min-height: 60px;
}

.btn-yes {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
}

.btn-yes:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.btn-no {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    border: none;
}

.btn-no:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.3);
}

.answer-icon {
    font-size: 1.25rem;
}

/* Question Navigation */
.question-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

/* Section Transition */
.section-completion {
    margin: 2rem 0;
}

.completion-animation {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: celebrate 1s ease-in-out;
}

@keyframes celebrate {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.completion-stats {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    color: #2c3e50;
    font-weight: 500;
}

/* Results Styles */
.recommendation-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: left;
}

.recommendation-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.recommendation-card p {
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.results-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.result-item:last-child {
    margin-bottom: 0;
}

.result-label {
    opacity: 0.8;
}

.result-value {
    font-weight: 600;
}
