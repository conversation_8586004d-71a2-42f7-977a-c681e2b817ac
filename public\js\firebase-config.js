// Firebase configuration for client-side (browser)
const firebaseConfig = {
  apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
  authDomain: "barefoot-elearning-app.firebaseapp.com",
  projectId: "barefoot-elearning-app",
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
  storageBucket: "barefoot-elearning-app.appspot.com",
  messagingSenderId: "170819735788",
  appId: "1:170819735788:web:223af318437eb5d947d5c9"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Get Firebase services
const auth = firebase.auth();
const db = firebase.firestore();

// Auth state observer
let firebaseCurrentUser = null;

auth.onAuthStateChanged((user) => {
  firebaseCurrentUser = user;
  if (user) {
    console.log('User signed in:', user.email);
    // Update UI for signed-in user
    updateUIForSignedInUser(user);
  } else {
    console.log('User signed out');
    // Update UI for signed-out user
    updateUIForSignedOutUser();
  }
});

// Helper functions
function updateUIForSignedInUser(user) {
  // Update the app's current user variable
  if (typeof window.appCurrentUser !== 'undefined') {
    window.appCurrentUser = user;
  }

  // Call the app's handler if available
  if (typeof handleUserSignedIn === 'function') {
    handleUserSignedIn(user);
  }
}

function updateUIForSignedOutUser() {
  // Clear the app's current user variable
  if (typeof window.appCurrentUser !== 'undefined') {
    window.appCurrentUser = null;
  }

  // Call the app's handler if available
  if (typeof handleUserSignedOut === 'function') {
    handleUserSignedOut();
  }
}
