// Assessment Data Structure
const assessmentData = {
    part1: {
        title: "Part 1: Basic IT Skills",
        description: "Entry Level",
        questions: [
            "I can turn on my phone, tablet or computer.",
            "I can unlock my phone, tablet or computer (e.g., by entering a password or passcode).",
            "I can use a mouse to single click and double click.",
            "I know where the Enter, Shift, Delete and Space Bar key is on a keyboard.",
            "I can use touchscreen on my phone or tablet.",
            "I can make text on the screen larger or smaller.",
            "I can open a browser (like Safari or Google Chrome).",
            "I can choose an app and open it."
        ],
        passingScore: 4,
        recommendation: "Computer Skills – Beginners"
    },
    part2: {
        title: "Part 2: Intermediate Digital Skills",
        description: "Improver Level",
        questions: [
            "I can put a new app on my device.",
            "I can do an internet search.",
            "I can create and save a document into a folder.",
            "I can solve basic technical problems myself.",
            "I can create a document using text and images.",
            "I can do some basic formatting (like changing font colour, underlining text or changing the size of a picture).",
            "I can take a screenshot.",
            "I can add people to my contacts.",
            "I can send an email.",
            "I can send a photo.",
            "I can take part in a video call.",
            "I can fill out an online form (like a repeat prescription request).",
            "I can buy something online.",
            "I can set a strong password.",
            "I can lock my device.",
            "I can check that a website is secure."
        ],
        passingScore: 16, // Must get ALL questions right
        recommendation: "Computer Skills – Improvers"
    },
    part3: {
        title: "Part 3: Confident User Skills",
        description: "Level 1",
        questions: [
            "I can create a system of folders for storing files.",
            "I can refine searches.",
            "I can find solutions online to help me sort out technical problems.",
            "I can put graphics and charts into formal documents.",
            "I can create and format a spreadsheet.",
            "I can use a spreadsheet to do simple calculations and produce basic charts.",
            "I can send work emails written in a different style from those to friends.",
            "I can keep my digital footprint to a minimum and manage my online identity.",
            "I can compare prices and check delivery costs when I'm buying something online.",
            "I can spot a phishing (dodgy) email.",
            "I can report offensive things I see online.",
            "I can protect my personal data and privacy online.",
            "I can set up and use multifactor authentication (like a password AND a PIN for an online account).",
            "I can use digital wellbeing tools (like setting a limit for how much time I spend on an app)."
        ],
        recommendations: {
            low: "Computer Skills for Everyday Life – Introduction (Level 1)", // 1-10 yes answers
            high: "Congratulations! You have strong digital skills" // 11-14 yes answers
        }
    }
};

// Assessment State
let currentAssessment = {
    currentPart: 1,
    currentQuestion: 0,
    answers: [],
    startTime: null,
    part1Score: 0,
    part2Score: 0,
    part3Score: 0,
    finalRecommendation: '',
    totalQuestionsAnswered: 0
};

// AI Assistant Messages
const aiMessages = {
    welcome: {
        title: "Hello! I'm your Digital Skills Assistant",
        message: "I'm here to help you discover your digital skills level. Please answer honestly - there are no wrong answers!"
    },
    sectionContinue: {
        title: "Well done!",
        message: "You're doing great. Ready to try the next section?"
    },
    sectionStop: {
        title: "Perfect!",
        message: "I've found the ideal course to help you build your skills."
    },
    finalCompletion: {
        title: "Excellent work!",
        message: "You've completed the full assessment. Here's what I recommend for you:"
    },
    strongSkills: {
        title: "Congratulations!",
        message: "You have demonstrated strong digital skills across all areas!"
    }
};

// Assessment Functions
function startAssessment() {
    currentAssessment = {
        currentPart: 1,
        currentQuestion: 0,
        answers: [],
        startTime: new Date(),
        part1Score: 0,
        part2Score: 0,
        part3Score: 0,
        finalRecommendation: '',
        totalQuestionsAnswered: 0
    };
    
    showAssessmentQuestion();
}

function showAssessmentQuestion() {
    const partKey = `part${currentAssessment.currentPart}`;
    const part = assessmentData[partKey];
    const questionIndex = currentAssessment.currentQuestion;
    
    if (questionIndex >= part.questions.length) {
        // Section completed, calculate score and determine next step
        calculateSectionScore();
        return;
    }
    
    // Update UI
    updateQuestionDisplay(part, questionIndex);
    showPage('assessmentQuestion');
}

function updateQuestionDisplay(part, questionIndex) {
    const totalQuestions = part.questions.length;
    const questionNumber = questionIndex + 1;
    
    // Update question counter
    document.getElementById('questionCounter').textContent = 
        `Question ${questionNumber} of ${totalQuestions}`;
    
    // Update progress bar
    const progress = (questionNumber / totalQuestions) * 100;
    document.getElementById('questionProgressFill').style.width = `${progress}%`;
    
    // Update section indicator
    document.getElementById('sectionIndicator').textContent = part.title;
    
    // Update question text
    document.getElementById('questionText').textContent = part.questions[questionIndex];
    
    // Show/hide previous button
    const prevBtn = document.getElementById('prevQuestionBtn');
    if (currentAssessment.currentQuestion === 0 && currentAssessment.currentPart === 1) {
        prevBtn.style.display = 'none';
    } else {
        prevBtn.style.display = 'inline-block';
    }
}

function answerQuestion(answer) {
    const partKey = `part${currentAssessment.currentPart}`;
    const questionId = `${partKey}_q${currentAssessment.currentQuestion + 1}`;
    
    // Store the answer
    const answerData = {
        questionId: questionId,
        partNumber: currentAssessment.currentPart,
        questionIndex: currentAssessment.currentQuestion,
        question: assessmentData[partKey].questions[currentAssessment.currentQuestion],
        answer: answer,
        timestamp: new Date()
    };
    
    // Update or add answer
    const existingIndex = currentAssessment.answers.findIndex(a => a.questionId === questionId);
    if (existingIndex >= 0) {
        currentAssessment.answers[existingIndex] = answerData;
    } else {
        currentAssessment.answers.push(answerData);
        currentAssessment.totalQuestionsAnswered++;
    }
    
    // Move to next question
    currentAssessment.currentQuestion++;
    showAssessmentQuestion();
}

function previousQuestion() {
    if (currentAssessment.currentQuestion > 0) {
        currentAssessment.currentQuestion--;
        showAssessmentQuestion();
    } else if (currentAssessment.currentPart > 1) {
        // Go back to previous section
        currentAssessment.currentPart--;
        const partKey = `part${currentAssessment.currentPart}`;
        currentAssessment.currentQuestion = assessmentData[partKey].questions.length - 1;
        showAssessmentQuestion();
    }
}

function skipQuestion() {
    // Treat skip as "No" answer
    answerQuestion('no');
}

function calculateSectionScore() {
    const partKey = `part${currentAssessment.currentPart}`;
    const part = assessmentData[partKey];
    
    // Count "yes" answers for current part
    const yesAnswers = currentAssessment.answers.filter(answer => 
        answer.partNumber === currentAssessment.currentPart && answer.answer === 'yes'
    ).length;
    
    // Store score
    currentAssessment[`part${currentAssessment.currentPart}Score`] = yesAnswers;
    
    // Determine next step based on scoring logic
    if (currentAssessment.currentPart === 1) {
        if (yesAnswers <= 3) {
            // Stop assessment, recommend beginners course
            currentAssessment.finalRecommendation = part.recommendation;
            showAssessmentResults();
        } else {
            // Continue to Part 2
            showSectionTransition(true);
        }
    } else if (currentAssessment.currentPart === 2) {
        if (yesAnswers < 16) {
            // Stop assessment, recommend improvers course
            currentAssessment.finalRecommendation = part.recommendation;
            showAssessmentResults();
        } else {
            // Continue to Part 3
            showSectionTransition(true);
        }
    } else if (currentAssessment.currentPart === 3) {
        // Final part completed
        if (yesAnswers >= 1 && yesAnswers <= 10) {
            currentAssessment.finalRecommendation = part.recommendations.low;
        } else {
            currentAssessment.finalRecommendation = part.recommendations.high;
        }
        showAssessmentResults();
    }
}

function showSectionTransition(continuing = true) {
    const message = continuing ? aiMessages.sectionContinue : aiMessages.sectionStop;
    
    document.getElementById('transitionTitle').textContent = message.title;
    document.getElementById('transitionMessage').textContent = message.message;
    
    const score = currentAssessment[`part${currentAssessment.currentPart}Score`];
    const total = assessmentData[`part${currentAssessment.currentPart}`].questions.length;
    document.getElementById('sectionScore').textContent = 
        `You answered "Yes" to ${score} out of ${total} questions`;
    
    // Show/hide buttons based on whether continuing
    const continueBtn = document.getElementById('continueToNextSection');
    const resultsBtn = document.getElementById('viewCurrentResults');
    
    if (continuing) {
        continueBtn.style.display = 'inline-block';
        continueBtn.textContent = currentAssessment.currentPart === 2 ? 
            'Continue to Final Section' : 'Continue to Next Section';
        resultsBtn.style.display = 'inline-block';
    } else {
        continueBtn.style.display = 'none';
        resultsBtn.style.display = 'inline-block';
        resultsBtn.textContent = 'View My Results';
    }
    
    showPage('sectionTransition');
}

function continueToNextSection() {
    currentAssessment.currentPart++;
    currentAssessment.currentQuestion = 0;
    showAssessmentQuestion();
}

function showAssessmentResults() {
    // Determine final message
    let message;
    if (currentAssessment.finalRecommendation.includes('Congratulations')) {
        message = aiMessages.strongSkills;
    } else {
        message = aiMessages.finalCompletion;
    }

    document.getElementById('resultsTitle').textContent = message.title;
    document.getElementById('resultsMessage').textContent = message.message;

    // Update recommendation
    document.getElementById('recommendedCourse').textContent = currentAssessment.finalRecommendation;

    // Set course description
    let description = "This course is perfect for building your foundational digital skills.";
    if (currentAssessment.finalRecommendation.includes('Improvers')) {
        description = "This course will help you develop intermediate digital skills for everyday use.";
    } else if (currentAssessment.finalRecommendation.includes('Level 1')) {
        description = "This course covers advanced digital skills for confident computer users.";
    } else if (currentAssessment.finalRecommendation.includes('Congratulations')) {
        description = "You've demonstrated excellent digital literacy across all skill levels!";
    }
    document.getElementById('courseDescription').textContent = description;

    // Update results summary
    document.getElementById('totalAnswered').textContent = currentAssessment.totalQuestionsAnswered;

    const timeSpent = Math.round((new Date() - currentAssessment.startTime) / 60000);
    document.getElementById('timeSpent').textContent = `${timeSpent} minutes`;

    document.getElementById('assessmentDate').textContent = new Date().toLocaleDateString();

    // Save results to Firebase
    saveAssessmentResults();

    // Use the global showPage function from app.js
    if (typeof window.showPage === 'function') {
        window.showPage('assessmentResults');
    }
}

async function saveAssessmentResults() {
    if (!appCurrentUser) return;
    
    try {
        const token = await appCurrentUser.getIdToken();
        const assessmentResult = {
            userId: appCurrentUser.email,
            completedAt: new Date().toISOString(),
            part1Score: currentAssessment.part1Score,
            part2Score: currentAssessment.part2Score,
            part3Score: currentAssessment.part3Score,
            finalRecommendation: currentAssessment.finalRecommendation,
            totalQuestionsAnswered: currentAssessment.totalQuestionsAnswered,
            timeSpent: Math.round((new Date() - currentAssessment.startTime) / 60000),
            answers: currentAssessment.answers
        };
        
        const response = await fetch('/api/assessment/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(assessmentResult)
        });
        
        if (!response.ok) {
            throw new Error('Failed to save assessment results');
        }
        
        console.log('Assessment results saved successfully');
    } catch (error) {
        console.error('Failed to save assessment results:', error);
    }
}

function retakeAssessment() {
    startAssessment();
}

// Make functions globally available
window.startAssessment = startAssessment;
window.answerQuestion = answerQuestion;
window.previousQuestion = previousQuestion;
window.skipQuestion = skipQuestion;
window.continueToNextSection = continueToNextSection;
window.retakeAssessment = retakeAssessment;
