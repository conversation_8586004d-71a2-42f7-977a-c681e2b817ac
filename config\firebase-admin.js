// Firebase Admin SDK configuration for server-side
const admin = require('firebase-admin');
require('dotenv').config();

// For development without service account credentials, we'll create a mock implementation
// In production, you would use proper Firebase Admin SDK with service account credentials

let db, auth;

if (process.env.NODE_ENV === 'production') {
  // Production configuration with proper credentials
  if (!admin.apps.length) {
    admin.initializeApp({
      projectId: process.env.FIREBASE_PROJECT_ID,
      databaseURL: process.env.FIREBASE_DATABASE_URL,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET
    });
  }
  db = admin.firestore();
  auth = admin.auth();
} else {
  // Development mock implementation
  console.log('Running in development mode with mock Firebase Admin');

  // Mock Firestore
  db = {
    collection: (name) => ({
      doc: (id) => ({
        set: async (data) => {
          console.log(`Mock: Setting document ${id} in collection ${name}:`, data);
          return { id };
        },
        get: async () => {
          console.log(`Mock: Getting document ${id} from collection ${name}`);
          return {
            exists: true,
            data: () => ({
              name: 'Mock User',
              email: '<EMAIL>',
              assessmentStatus: 'not_started',
              createdAt: new Date()
            })
          };
        },
        update: async (data) => {
          console.log(`Mock: Updating document ${id} in collection ${name}:`, data);
          return { id };
        }
      }),
      add: async (data) => {
        const mockId = 'mock_' + Date.now();
        console.log(`Mock: Adding document to collection ${name}:`, data);
        return { id: mockId };
      },
      get: async () => {
        console.log(`Mock: Getting collection ${name}`);
        return {
          empty: false,
          docs: [
            {
              id: 'mock1',
              data: () => ({
                name: 'Mock User 1',
                email: '<EMAIL>',
                assessmentStatus: 'completed'
              })
            },
            {
              id: 'mock2',
              data: () => ({
                name: 'Mock User 2',
                email: '<EMAIL>',
                assessmentStatus: 'not_started'
              })
            }
          ],
          forEach: function(callback) {
            this.docs.forEach(callback);
          }
        };
      },
      where: (field, op, value) => ({
        orderBy: (field, direction) => ({
          limit: (num) => ({
            get: async () => {
              console.log(`Mock: Querying collection ${name} where ${field} ${op} ${value}`);
              return {
                empty: false,
                docs: [{
                  data: () => ({
                    finalRecommendation: 'Computer Skills – Beginners',
                    totalQuestionsAnswered: 8,
                    timeSpent: 5,
                    completedAt: new Date().toISOString()
                  })
                }]
              };
            }
          })
        })
      })
    })
  };

  // Mock Auth
  auth = {
    verifyIdToken: async (token) => {
      console.log('Mock: Verifying ID token:', token.substring(0, 20) + '...');
      return {
        uid: 'mock_uid',
        email: '<EMAIL>',
        name: 'Mock User'
      };
    }
  };

  // Mock admin object
  admin.firestore = {
    FieldValue: {
      serverTimestamp: () => new Date()
    }
  };
}

module.exports = {
  admin,
  db,
  auth
};
