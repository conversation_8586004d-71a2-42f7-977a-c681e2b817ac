# Digital Skills Assessment Web App

A comprehensive web application for digital skills assessment and course recommendation, powered by AI and built with modern web technologies.

## Features

### Part 1 - User Authentication & Onboarding ✅

- **Landing Page**: Clean interface with options for students and administrators
- **Student Registration**: Multi-step form collecting:
  - Personal information (name, age, email)
  - Account credentials (password)
  - Preferences (language, previous study experience)
  - Accessibility needs (optional)
- **Student Login**: Secure authentication for returning users
- **Admin Login**: Separate authentication for administrators
- **User Dashboards**: Personalized interfaces for both students and admins

## Tech Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js with Express.js
- **Database**: Firebase Firestore
- **Authentication**: Firebase Authentication
- **Styling**: Custom CSS with responsive design
- **Icons**: Unicode emojis for accessibility

## Project Structure

```
├── config/
│   ├── firebase.js           # Client-side Firebase config
│   └── firebase-admin.js     # Server-side Firebase Admin config
├── public/
│   ├── css/
│   │   └── styles.css        # Main stylesheet
│   ├── js/
│   │   ├── firebase-config.js # Firebase client configuration
│   │   └── app.js            # Main application logic
│   └── index.html            # Main HTML file
├── server.js                 # Express server
├── package.json              # Dependencies and scripts
└── .env                      # Environment variables
```

## Setup Instructions

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Firebase project with Authentication and Firestore enabled

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd digital-skills-assessment
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   - Copy `.env.example` to `.env`
   - Update Firebase configuration values
   - Set OpenAI API key (for future AI features)

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Open your browser**
   - Navigate to `http://localhost:3001`

## Environment Variables

```env
# Firebase Configuration
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_auth_domain
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_DATABASE_URL=your_database_url
FIREBASE_STORAGE_BUCKET=your_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# OpenAI Configuration (for future use)
OPENAI_API_KEY=your_openai_key

# Server Configuration
PORT=3001
NODE_ENV=development
```

## API Endpoints

### Authentication & User Management

- `POST /api/register` - Register new student
- `GET /api/profile` - Get user profile (authenticated)
- `PUT /api/profile` - Update user profile (authenticated)
- `GET /api/admin/users` - Get all users (admin only)

## Features Implemented

### ✅ User Authentication
- Firebase Authentication integration
- Email/password registration and login
- Separate admin authentication
- Secure token-based API authentication

### ✅ Responsive Design
- Mobile-first approach
- Accessible design with proper focus states
- High contrast mode support
- Reduced motion support for accessibility

### ✅ Multi-step Registration
- Progressive form with validation
- Real-time error handling
- Success/error messaging
- Form persistence between steps

### ✅ User Dashboards
- Student dashboard with assessment status
- Admin dashboard with user statistics
- Progress tracking (placeholder for assessment)

## Upcoming Features (Future Parts)

### Part 2 - Assessment Journey
- Dynamic question flow
- Adaptive questioning logic
- Progress tracking
- Answer validation

### Part 3 - AI Integration
- OpenAI-powered question generation
- Personalized feedback
- Course recommendations
- Adaptive learning paths

### Part 4 - Admin Dashboard
- User management
- Assessment analytics
- Data export functionality
- Reporting tools

### Part 5 - Data Model Enhancement
- Advanced Firestore schemas
- Real-time data synchronization
- Offline support
- Data backup strategies

### Part 6 - Production Ready
- Enhanced accessibility features
- Comprehensive error handling
- Deployment configuration
- Performance optimization

## Development Guidelines

### Code Style
- Use ES6+ features
- Follow semantic HTML practices
- Implement progressive enhancement
- Maintain accessibility standards

### Security
- All API endpoints require authentication
- Input validation on both client and server
- Secure Firebase rules (to be implemented)
- Environment variable protection

### Testing
- Manual testing completed for authentication flows
- Automated testing to be added in future iterations

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Contributing

This project is part of a structured development approach. Each part builds upon the previous one:

1. **Part 1**: Authentication & Onboarding ✅
2. **Part 2**: Assessment Journey (Next)
3. **Part 3**: AI Integration
4. **Part 4**: Admin Dashboard
5. **Part 5**: Data Model
6. **Part 6**: Production Polish

## License

This project is licensed under the ISC License.

## Support

For questions or issues, please refer to the project documentation or contact the development team.
