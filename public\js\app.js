// Global variables
let currentStep = 1;
let appCurrentUser = null;

// Make appCurrentUser globally accessible
window.appCurrentUser = appCurrentUser;

// DOM Elements
const pages = {
    landing: document.getElementById('landingPage'),
    studentOptions: document.getElementById('studentOptions'),
    registration: document.getElementById('registrationPage'),
    studentLogin: document.getElementById('studentLoginPage'),
    adminLogin: document.getElementById('adminLoginPage'),
    studentDashboard: document.getElementById('studentDashboard'),
    adminDashboard: document.getElementById('adminDashboard'),
    assessmentWelcome: document.getElementById('assessmentWelcome'),
    assessmentQuestion: document.getElementById('assessmentQuestion'),
    sectionTransition: document.getElementById('sectionTransition'),
    assessmentResults: document.getElementById('assessmentResults')
};

const forms = {
    registration: document.getElementById('registrationForm'),
    studentLogin: document.getElementById('studentLoginForm'),
    adminLogin: document.getElementById('adminLoginForm')
};

const loadingOverlay = document.getElementById('loadingOverlay');

// Utility functions
function showLoading() {
    loadingOverlay.classList.add('active');
}

function hideLoading() {
    loadingOverlay.classList.remove('active');
}

function showPage(pageKey) {
    // Hide all pages
    Object.values(pages).forEach(page => {
        if (page) page.classList.remove('active');
    });
    
    // Show selected page
    if (pages[pageKey]) {
        pages[pageKey].classList.add('active');
    }
    
    // Update progress bar (placeholder for now)
    updateProgressBar(pageKey);
}

function updateProgressBar(pageKey) {
    const progressFill = document.querySelector('.progress-fill');
    const progressMap = {
        landing: 0,
        studentOptions: 10,
        registration: 25,
        studentLogin: 25,
        adminLogin: 25,
        studentDashboard: 50,
        adminDashboard: 50,
        assessmentWelcome: 60,
        assessmentQuestion: 75,
        sectionTransition: 85,
        assessmentResults: 100
    };

    const progress = progressMap[pageKey] || 0;
    progressFill.style.width = `${progress}%`;
}

function showError(message, containerId = null) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    if (containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.insertBefore(errorDiv, container.firstChild);
        }
    } else {
        // Show in current active page
        const activePage = document.querySelector('.page.active');
        if (activePage) {
            const card = activePage.querySelector('.form-card, .welcome-card, .options-card, .dashboard-card');
            if (card) {
                card.insertBefore(errorDiv, card.firstChild);
            }
        }
    }
    
    // Remove error after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

function showSuccess(message, containerId = null) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    if (containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.insertBefore(successDiv, container.firstChild);
        }
    } else {
        // Show in current active page
        const activePage = document.querySelector('.page.active');
        if (activePage) {
            const card = activePage.querySelector('.form-card, .welcome-card, .options-card, .dashboard-card');
            if (card) {
                card.insertBefore(successDiv, card.firstChild);
            }
        }
    }
    
    // Remove success message after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

// Navigation functions
function showLandingPage() {
    showPage('landing');
}

function showStudentOptions() {
    showPage('studentOptions');
}

function showRegistration() {
    currentStep = 1;
    showFormStep(1);
    showPage('registration');
}

function showStudentLogin() {
    showPage('studentLogin');
}

function showAdminLogin() {
    showPage('adminLogin');
}

function showStudentDashboard() {
    showPage('studentDashboard');
    loadStudentDashboard();
}

function showAdminDashboard() {
    showPage('adminDashboard');
    loadAdminDashboard();
}

function showAssessmentWelcome() {
    showPage('assessmentWelcome');
}

function showAssessmentResults() {
    showPage('assessmentResults');
}

// Multi-step form functions
function showFormStep(step) {
    const steps = document.querySelectorAll('.form-step');
    steps.forEach((stepEl, index) => {
        stepEl.classList.toggle('active', index + 1 === step);
    });
    currentStep = step;
}

function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < 2) {
            showFormStep(currentStep + 1);
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        showFormStep(currentStep - 1);
    }
}

function validateCurrentStep() {
    const currentStepEl = document.querySelector(`.form-step[data-step="${currentStep}"]`);
    const requiredFields = currentStepEl.querySelectorAll('[required]');
    
    for (let field of requiredFields) {
        if (!field.value.trim()) {
            field.focus();
            showError(`Please fill in the ${field.labels[0].textContent.replace(' *', '')} field.`);
            return false;
        }
        
        // Additional validation
        if (field.type === 'email' && !isValidEmail(field.value)) {
            field.focus();
            showError('Please enter a valid email address.');
            return false;
        }
        
        if (field.type === 'password' && field.value.length < 6) {
            field.focus();
            showError('Password must be at least 6 characters long.');
            return false;
        }
    }
    
    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Firebase Auth handlers
function handleUserSignedIn(user) {
    appCurrentUser = user;
    window.appCurrentUser = user;

    // Determine user type and redirect accordingly
    if (user.email && user.email.includes('admin')) {
        showAdminDashboard();
    } else {
        showStudentDashboard();
    }
}

function handleUserSignedOut() {
    appCurrentUser = null;
    window.appCurrentUser = null;
    showLandingPage();
}

// Registration form handler
if (forms.registration) {
    forms.registration.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        if (!validateCurrentStep()) {
            return;
        }
        
        showLoading();
        
        try {
            const formData = new FormData(forms.registration);
            const userData = {
                name: formData.get('name'),
                age: formData.get('age'),
                email: formData.get('email'),
                password: formData.get('password'),
                preferredLanguage: formData.get('preferredLanguage'),
                hasStudiedBefore: formData.get('hasStudiedBefore'),
                supportNeeds: formData.get('supportNeeds'),
                userType: 'student'
            };
            
            // Create Firebase user
            const userCredential = await auth.createUserWithEmailAndPassword(userData.email, userData.password);
            
            // Update user profile
            await userCredential.user.updateProfile({
                displayName: userData.name
            });

            // Save additional user data to backend
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                throw new Error('Failed to save user profile');
            }

            showSuccess('Account created successfully! Welcome!');

            // User will be automatically redirected by auth state change
            
        } catch (error) {
            console.error('Registration error:', error);
            showError(error.message || 'Failed to create account. Please try again.');
        } finally {
            hideLoading();
        }
    });
}

// Student login form handler
if (forms.studentLogin) {
    forms.studentLogin.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        showLoading();
        
        try {
            const formData = new FormData(forms.studentLogin);
            const email = formData.get('email');
            const password = formData.get('password');
            
            await auth.signInWithEmailAndPassword(email, password);
            
            showSuccess('Welcome back!');
            
            // User will be automatically redirected by auth state change
            
        } catch (error) {
            console.error('Login error:', error);
            showError(error.message || 'Failed to sign in. Please check your credentials.');
        } finally {
            hideLoading();
        }
    });
}

// Admin login form handler
if (forms.adminLogin) {
    forms.adminLogin.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        showLoading();
        
        try {
            const formData = new FormData(forms.adminLogin);
            const email = formData.get('email');
            const password = formData.get('password');
            
            // Simple admin check - in production, you'd want proper role-based auth
            if (!email.includes('admin')) {
                throw new Error('Admin access required');
            }
            
            await auth.signInWithEmailAndPassword(email, password);
            
            showSuccess('Admin login successful!');
            
            // User will be automatically redirected by auth state change
            
        } catch (error) {
            console.error('Admin login error:', error);
            showError(error.message || 'Failed to sign in as admin. Please check your credentials.');
        } finally {
            hideLoading();
        }
    });
}

// Dashboard functions
async function loadStudentDashboard() {
    if (!appCurrentUser) return;

    try {
        const token = await appCurrentUser.getIdToken();
        const response = await fetch('/api/profile', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const profile = await response.json();

            // Update dashboard with user data
            const nameEl = document.getElementById('studentName');
            if (nameEl) nameEl.textContent = profile.name || appCurrentUser.displayName || 'Student';

            const statusEl = document.getElementById('assessmentStatus');
            const continueBtn = document.getElementById('continueAssessmentBtn');
            const viewResultsBtn = document.getElementById('viewResultsBtn');

            if (profile.assessmentStatus === 'completed') {
                if (statusEl) statusEl.textContent = 'Completed';
                if (continueBtn) continueBtn.textContent = 'Retake Assessment';
                if (viewResultsBtn) {
                    viewResultsBtn.style.display = 'inline-block';
                    viewResultsBtn.addEventListener('click', loadAndShowResults);
                }

                // Update progress
                const progressEl = document.getElementById('progressPercent');
                if (progressEl) progressEl.textContent = '100%';
            } else {
                if (statusEl) statusEl.textContent = 'Not started';
                if (continueBtn) continueBtn.textContent = 'Start Assessment';
                if (viewResultsBtn) viewResultsBtn.style.display = 'none';

                // Update progress
                const progressEl = document.getElementById('progressPercent');
                if (progressEl) progressEl.textContent = '0%';
            }
        }
    } catch (error) {
        console.error('Failed to load student dashboard:', error);
    }
}

async function loadAndShowResults() {
    if (!appCurrentUser) return;

    try {
        const token = await appCurrentUser.getIdToken();
        const response = await fetch('/api/assessment/results', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const results = await response.json();

            // Update results page with saved data
            document.getElementById('recommendedCourse').textContent = results.finalRecommendation;
            document.getElementById('totalAnswered').textContent = results.totalQuestionsAnswered;
            document.getElementById('timeSpent').textContent = `${results.timeSpent} minutes`;

            const completedDate = new Date(results.completedAt);
            document.getElementById('assessmentDate').textContent = completedDate.toLocaleDateString();

            showPage('assessmentResults');
        }
    } catch (error) {
        console.error('Failed to load assessment results:', error);
        showError('Failed to load your assessment results.');
    }
}

async function loadAdminDashboard() {
    if (!appCurrentUser) return;

    try {
        const token = await appCurrentUser.getIdToken();

        // Load users data
        const usersResponse = await fetch('/api/admin/users', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        // Load assessments data
        const assessmentsResponse = await fetch('/api/admin/assessments', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (usersResponse.ok && assessmentsResponse.ok) {
            const users = await usersResponse.json();
            const assessments = await assessmentsResponse.json();

            // Update admin stats
            const totalEl = document.getElementById('totalStudents');
            if (totalEl) totalEl.textContent = users.length;

            const activeEl = document.getElementById('activeAssessments');
            if (activeEl) {
                const inProgress = users.filter(u => u.assessmentStatus === 'in_progress').length;
                activeEl.textContent = inProgress;
            }

            const completedEl = document.getElementById('completedAssessments');
            if (completedEl) {
                const completed = users.filter(u => u.assessmentStatus === 'completed').length;
                completedEl.textContent = completed;
            }
        }
    } catch (error) {
        console.error('Failed to load admin dashboard:', error);
    }
}

// Sign out function
async function signOut() {
    try {
        await auth.signOut();
        showSuccess('Signed out successfully!');
    } catch (error) {
        console.error('Sign out error:', error);
        showError('Failed to sign out.');
    }
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    // Show landing page by default
    showLandingPage();

    // Remove any existing error/success messages
    document.querySelectorAll('.error-message, .success-message').forEach(el => el.remove());

    // Add event listeners for navigation buttons
    setupEventListeners();
});

// Setup all event listeners
function setupEventListeners() {
    // Landing page buttons
    const startAssessmentBtn = document.getElementById('startAssessmentBtn');
    const adminLoginBtn = document.getElementById('adminLoginBtn');

    if (startAssessmentBtn) startAssessmentBtn.addEventListener('click', showStudentOptions);
    if (adminLoginBtn) adminLoginBtn.addEventListener('click', showAdminLogin);

    // Student options buttons
    const newStudentBtn = document.getElementById('newStudentBtn');
    const returningStudentBtn = document.getElementById('returningStudentBtn');
    const backToMainBtn = document.getElementById('backToMainBtn');

    if (newStudentBtn) newStudentBtn.addEventListener('click', showRegistration);
    if (returningStudentBtn) returningStudentBtn.addEventListener('click', showStudentLogin);
    if (backToMainBtn) backToMainBtn.addEventListener('click', showLandingPage);

    // Registration form buttons
    const nextStepBtn = document.getElementById('nextStepBtn');
    const prevStepBtn = document.getElementById('prevStepBtn');
    const backToStudentOptionsBtn1 = document.getElementById('backToStudentOptionsBtn1');

    if (nextStepBtn) nextStepBtn.addEventListener('click', nextStep);
    if (prevStepBtn) prevStepBtn.addEventListener('click', prevStep);
    if (backToStudentOptionsBtn1) backToStudentOptionsBtn1.addEventListener('click', showStudentOptions);

    // Student login buttons
    const signUpLink = document.getElementById('signUpLink');
    const backToStudentOptionsBtn2 = document.getElementById('backToStudentOptionsBtn2');

    if (signUpLink) signUpLink.addEventListener('click', (e) => {
        e.preventDefault();
        showRegistration();
    });
    if (backToStudentOptionsBtn2) backToStudentOptionsBtn2.addEventListener('click', showStudentOptions);

    // Admin login buttons
    const backToMainMenuBtn = document.getElementById('backToMainMenuBtn');
    if (backToMainMenuBtn) backToMainMenuBtn.addEventListener('click', showLandingPage);

    // Sign out buttons
    const signOutBtn1 = document.getElementById('signOutBtn1');
    const signOutBtn2 = document.getElementById('signOutBtn2');

    if (signOutBtn1) signOutBtn1.addEventListener('click', signOut);
    if (signOutBtn2) signOutBtn2.addEventListener('click', signOut);

    // Assessment buttons
    const continueAssessmentBtn = document.getElementById('continueAssessmentBtn');
    const startAssessmentMainBtn = document.getElementById('startAssessmentMainBtn');
    const backToDashboardBtn = document.getElementById('backToDashboardBtn');
    const answerYesBtn = document.getElementById('answerYes');
    const answerNoBtn = document.getElementById('answerNo');
    const prevQuestionBtn = document.getElementById('prevQuestionBtn');
    const skipQuestionBtn = document.getElementById('skipQuestionBtn');
    const continueToNextSectionBtn = document.getElementById('continueToNextSection');
    const viewCurrentResultsBtn = document.getElementById('viewCurrentResults');
    const retakeAssessmentBtn = document.getElementById('retakeAssessmentBtn');
    const backToDashboardFromResultsBtn = document.getElementById('backToDashboardFromResults');

    if (continueAssessmentBtn) continueAssessmentBtn.addEventListener('click', showAssessmentWelcome);
    if (startAssessmentMainBtn) startAssessmentMainBtn.addEventListener('click', startAssessment);
    if (backToDashboardBtn) backToDashboardBtn.addEventListener('click', showStudentDashboard);
    if (answerYesBtn) answerYesBtn.addEventListener('click', () => answerQuestion('yes'));
    if (answerNoBtn) answerNoBtn.addEventListener('click', () => answerQuestion('no'));
    if (prevQuestionBtn) prevQuestionBtn.addEventListener('click', previousQuestion);
    if (skipQuestionBtn) skipQuestionBtn.addEventListener('click', skipQuestion);
    if (continueToNextSectionBtn) continueToNextSectionBtn.addEventListener('click', continueToNextSection);
    if (viewCurrentResultsBtn) viewCurrentResultsBtn.addEventListener('click', showAssessmentResults);
    if (retakeAssessmentBtn) retakeAssessmentBtn.addEventListener('click', retakeAssessment);
    if (backToDashboardFromResultsBtn) backToDashboardFromResultsBtn.addEventListener('click', showStudentDashboard);
}

// Make essential functions globally accessible for assessment integration
window.showPage = showPage;
window.showAssessmentResults = showAssessmentResults;
