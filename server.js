const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const bodyParser = require('body-parser');
const path = require('path');
require('dotenv').config();

const { admin, db, auth } = require('./config/firebase-admin');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://www.gstatic.com", "https://apis.google.com"],
      scriptSrcAttr: ["'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      connectSrc: ["'self'", "https://identitytoolkit.googleapis.com", "https://firestore.googleapis.com", "https://securetoken.googleapis.com"]
    }
  }
}));
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Middleware to verify Firebase token
const verifyToken = async (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decodedToken = await auth.verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// User registration endpoint
app.post('/api/register', async (req, res) => {
  try {
    const { name, age, email, preferredLanguage, hasStudiedBefore, supportNeeds, userType } = req.body;
    
    // Store user profile in Firestore
    const userProfile = {
      name,
      age: parseInt(age),
      email,
      preferredLanguage,
      hasStudiedBefore: hasStudiedBefore === 'yes',
      supportNeeds: supportNeeds || '',
      userType: userType || 'student',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      assessmentStatus: 'not_started'
    };

    await db.collection('users').doc(email).set(userProfile);
    
    res.json({ success: true, message: 'User profile created successfully' });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Failed to create user profile' });
  }
});

// Get user profile
app.get('/api/profile', verifyToken, async (req, res) => {
  try {
    const userDoc = await db.collection('users').doc(req.user.email).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User profile not found' });
    }
    
    res.json(userDoc.data());
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

// Update user profile
app.put('/api/profile', verifyToken, async (req, res) => {
  try {
    const updates = req.body;
    updates.updatedAt = admin.firestore.FieldValue.serverTimestamp();
    
    await db.collection('users').doc(req.user.email).update(updates);
    
    res.json({ success: true, message: 'Profile updated successfully' });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Assessment routes
app.post('/api/assessment/save', verifyToken, async (req, res) => {
  try {
    const assessmentData = req.body;
    assessmentData.userId = req.user.email;
    assessmentData.createdAt = admin.firestore.FieldValue.serverTimestamp();

    // Save assessment result
    const assessmentRef = await db.collection('assessments').add(assessmentData);

    // Update user profile with assessment completion
    await db.collection('users').doc(req.user.email).update({
      assessmentStatus: 'completed',
      lastAssessmentId: assessmentRef.id,
      lastAssessmentDate: admin.firestore.FieldValue.serverTimestamp(),
      recommendedCourse: assessmentData.finalRecommendation
    });

    res.json({ success: true, assessmentId: assessmentRef.id });
  } catch (error) {
    console.error('Assessment save error:', error);
    res.status(500).json({ error: 'Failed to save assessment results' });
  }
});

app.get('/api/assessment/results', verifyToken, async (req, res) => {
  try {
    const assessmentsSnapshot = await db.collection('assessments')
      .where('userId', '==', req.user.email)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get();

    if (assessmentsSnapshot.empty) {
      return res.status(404).json({ error: 'No assessment results found' });
    }

    const latestAssessment = assessmentsSnapshot.docs[0].data();
    res.json(latestAssessment);
  } catch (error) {
    console.error('Assessment results fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch assessment results' });
  }
});

// Admin routes
app.get('/api/admin/users', verifyToken, async (req, res) => {
  try {
    // Check if user is admin (you can implement admin role checking here)
    const usersSnapshot = await db.collection('users').get();
    const users = [];

    usersSnapshot.forEach(doc => {
      users.push({ id: doc.id, ...doc.data() });
    });

    res.json(users);
  } catch (error) {
    console.error('Admin users fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

app.get('/api/admin/assessments', verifyToken, async (req, res) => {
  try {
    const assessmentsSnapshot = await db.collection('assessments').get();
    const assessments = [];

    assessmentsSnapshot.forEach(doc => {
      assessments.push({ id: doc.id, ...doc.data() });
    });

    res.json(assessments);
  } catch (error) {
    console.error('Admin assessments fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch assessments' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Visit http://localhost:${PORT} to view the application`);
}).on('error', (err) => {
  console.error('Server error:', err);
});

module.exports = app;
